<!DOCTYPE html>
<!-- saved from url=(0055)https://www.immuno-compass.com/help/index.html#section3 -->
<html lang="en"><head><meta http-equiv="Content-Type" content="text/html; charset=UTF-8">
    
    <meta name="description" content="Help and documentation for Immuno-Compass: A research tool for immunotherapy response prediction.">
    <meta name="keywords" content="Help, Immuno-Compass, Immunotherapy, Transcriptomics, Documentation">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <link rel="icon" href="https://www.immuno-compass.com/image/compass_logo.png" type="image/x-icon">
    <title>COMPASS - Help &amp; Documentation</title>
    <style>
        body {
            margin: 0;
            font-family: Arial, sans-serif;
            display: flex;
            flex-direction: column;
            height: 100vh;
        }

        /* Navigation bar */
        header {
            background: #2c3e50;
            color: #fff;
        }

        .navbar {
            display: flex;
            justify-content: center;
        }

        .navbar ul {
            list-style: none;
            margin: 0;
            padding: 0;
            display: flex;
        }

        .navbar li {
            margin: 0;
        }

        .navbar a {
            display: block;
            padding: 15px 20px;
            text-decoration: none;
            color: #fff;
            font-weight: 500;
        }

        .navbar a:hover {
            background: #1a252f;
        }

        .navbar a.active {
            background: #3498db;
            color: #fff;
            font-weight: 600;
        }

        .main-container {
            display: flex;
            flex-grow: 1;
            overflow: hidden;
        }

        .sidebar {
            background: #f5f5f5;
            color: #333;
            width: 300px;
            padding: 20px;
            box-sizing: border-box;
            display: flex;
            flex-direction: column;
            overflow-y: auto;
            border-right: 1px solid #ddd;
        }

        .sidebar a {
            color: #333;
            text-decoration: none;
            font-weight: 500;
            padding: 10px;
            border-radius: 5px;
            margin-bottom: 10px;
        }

        .sidebar a:hover, .sidebar a.active {
            background: #3498db;
            color: #fff;
        }

        .content {
            flex-grow: 1;
            padding: 20px;
            overflow-y: auto;
        }

        .section {
            display: none;
        }

        .section.active {
            display: block;
        }

        .iframe-container {
            width: 100%;
            height: calc(100vh - 60px);
            border: none;
        }

        h1, h2 {
            margin: 0 0 20px;
        }
    </style>
</head>
<body>
    <header>
        <nav class="navbar">
            <ul>
                <li><a href="https://www.immuno-compass.com/index.html">Home</a></li>
                <li><a href="https://www.immuno-compass.com/predict/index.html">Predict</a></li>
                <li><a href="https://www.immuno-compass.com/extract/index.html">Extract</a></li>
                <li><a href="https://www.immuno-compass.com/explore/index.html">Explore</a></li>
                <li><a href="https://www.immuno-compass.com/help/index.html" class="active">Help</a></li>
                <li><a href="https://www.immuno-compass.com/download/index.html">Download</a></li>
                <li><a href="https://www.immuno-compass.com/about/index.html">About</a></li>
            </ul>
        </nav>
    </header>

    <div class="main-container">
        <div class="sidebar">
            <a href="https://www.immuno-compass.com/help/index.html#section1" class="" onclick="showSection(&#39;section1&#39;)">1. Preparing Your Data</a>
            <a href="https://www.immuno-compass.com/help/index.html#section2" onclick="showSection(&#39;section2&#39;)" class="">2. About Compass Datasets</a>
            <a href="https://www.immuno-compass.com/help/index.html#section3" onclick="showSection(&#39;section3&#39;)" class="active">3. Finetuning a New Model</a>
            <a href="https://www.immuno-compass.com/help/index.html#section4" onclick="showSection(&#39;section4&#39;)">4. Making Predictions</a>
            <a href="https://www.immuno-compass.com/help/index.html#section5" onclick="showSection(&#39;section5&#39;)">5. Feature Extraction</a>
            <a href="https://www.immuno-compass.com/help/index.html#section6" onclick="showSection(&#39;section6&#39;)">6. Source Code</a>
        </div>

        <div class="content">
            <div id="section1" class="section">
                <h2>1. Preparing Your Data</h2>
                <p>
                    Learn how to format, normalize, and prepare your transcriptomic data for analysis. Accepted file types include counts, TPM, and FPKM.
                </p>
                <iframe src="./COMPASS_finetune_new_model_files/00_prepare_data.html" class="iframe-container"></iframe>
            </div>
            <div id="section2" class="section">
                <h2>2. About COMPASS Datasets</h2>
                <p>An overview of the datasets utilized in COMPASS.</p>
                <iframe src="./COMPASS_finetune_new_model_files/01_ITRP_datasets_description.html" class="iframe-container"></iframe>
            </div>
            <div id="section3" class="section active">
                <h2>3. Finetuning a New Model</h2>
                <p>Step-by-step guide for offline model finetuning using Compass.</p>
                <iframe src="./COMPASS_finetune_new_model_files/02_compass_finetuning.html" class="iframe-container"></iframe>
            </div>
            <div id="section4" class="section">
                <h2>4. Making Predictions</h2>
                <p>Instructions on running predictions and interpreting output formats and error messages.</p>
                <iframe src="./COMPASS_finetune_new_model_files/03_compass_prediction.html" class="iframe-container"></iframe>
            </div>
            <div id="section5" class="section">
                <h2>5. Feature Extraction</h2>
                <p>Learn how to extract useful features for developing new predictive models.</p>
                <iframe src="./COMPASS_finetune_new_model_files/04_logstic_regression.html" class="iframe-container"></iframe>
            </div>
            <div id="section6" class="section">
                <h2>6. Source Code</h2>
                <p>Access the source code on GitHub and get started with local deployment.</p>
                    <ul>
                        <li><a href="https://github.com/mims-harvard/COMPASS" class="download-link" target="_blank">GitHub: COMPASS code</a></li>
                    </ul>
                    <ul>
                        <li><a href="https://github.com/mims-harvard/COMPASS-web/" class="download-link" target="_blank">GitHub: COMPASS data pre-processing code</a></li>
                    </ul>
                


                
            </div>
        </div>
    </div>

    <script>
        function showSection(sectionId) {
            document.querySelectorAll('.section').forEach(section => {
                section.classList.remove('active');
            });
            document.querySelectorAll('.sidebar a').forEach(link => {
                link.classList.remove('active');
            });
            document.getElementById(sectionId).classList.add('active');
            document.querySelector(`.sidebar a[href="#${sectionId}"]`).classList.add('active');
        }
    </script>


</body></html>